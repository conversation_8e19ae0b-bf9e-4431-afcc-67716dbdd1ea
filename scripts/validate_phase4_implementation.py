#!/usr/bin/env python3
"""
Phase 4 Implementation Validation Script
Validates that Phase 4 code structure and imports are correct
"""

import sys
import os
import ast
import logging
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class Phase4Validator:
    """Validates Phase 4 implementation without running the full system"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.validation_results = {}
    
    def validate_all(self) -> bool:
        """Run all validation checks"""
        logger.info("🔍 Validating Phase 4 Signal Quality Enhancement Implementation")
        
        validations = [
            ("Regime Detector Module", self.validate_regime_detector),
            ("Enhanced Signal Generator", self.validate_signal_generator),
            ("Market Intelligence Integration", self.validate_market_intelligence),
            ("Code Structure", self.validate_code_structure)
        ]
        
        passed = 0
        total = len(validations)
        
        for name, validator in validations:
            try:
                result = validator()
                if result:
                    logger.info(f"✅ {name}: VALID")
                    passed += 1
                else:
                    logger.error(f"❌ {name}: INVALID")
                self.validation_results[name] = result
            except Exception as e:
                logger.error(f"💥 {name}: ERROR - {e}")
                self.validation_results[name] = False
        
        success_rate = (passed / total) * 100
        logger.info(f"\n🎯 Validation Results: {passed}/{total} checks passed ({success_rate:.1f}%)")
        
        return success_rate >= 100  # Require 100% for code validation
    
    def validate_regime_detector(self) -> bool:
        """Validate regime detector implementation"""
        try:
            regime_detector_path = os.path.join(
                self.project_root, 'src', 'market_intelligence', 'regime_detector.py'
            )
            
            if not os.path.exists(regime_detector_path):
                logger.error("Regime detector file not found")
                return False
            
            with open(regime_detector_path, 'r') as f:
                content = f.read()
            
            # Check for required classes and methods
            required_elements = [
                'class MarketRegime',
                'class RegimeAnalysis',
                'class RegimeDetector',
                'detect_market_regime',
                'should_filter_signal_by_regime',
                'get_regime_adjusted_signal_multiplier'
            ]
            
            for element in required_elements:
                if element not in content:
                    logger.error(f"Missing required element: {element}")
                    return False
            
            # Parse AST to validate structure
            try:
                ast.parse(content)
            except SyntaxError as e:
                logger.error(f"Syntax error in regime detector: {e}")
                return False
            
            logger.info("Regime detector implementation validated")
            return True
            
        except Exception as e:
            logger.error(f"Regime detector validation failed: {e}")
            return False
    
    def validate_signal_generator(self) -> bool:
        """Validate enhanced signal generator"""
        try:
            signal_gen_path = os.path.join(
                self.project_root, 'src', 'signal_engine', 'signal_generator.py'
            )
            
            if not os.path.exists(signal_gen_path):
                logger.error("Signal generator file not found")
                return False
            
            with open(signal_gen_path, 'r') as f:
                content = f.read()
            
            # Check for Phase 4 enhancements
            phase4_elements = [
                'from src.market_intelligence import',
                'regime_detector',
                'volatility_analyzer',
                '_filter_assets_by_volatility',
                '_is_valid_enhanced_signal',
                '_enhance_signal_with_market_intelligence',
                '_calculate_enhanced_signal_score',
                'volatility_regime_filter',
                'market_regime_filter',
                'min_volatility_expansion_threshold'
            ]
            
            for element in phase4_elements:
                if element not in content:
                    logger.error(f"Missing Phase 4 element: {element}")
                    return False
            
            # Parse AST to validate structure
            try:
                ast.parse(content)
            except SyntaxError as e:
                logger.error(f"Syntax error in signal generator: {e}")
                return False
            
            logger.info("Enhanced signal generator validated")
            return True
            
        except Exception as e:
            logger.error(f"Signal generator validation failed: {e}")
            return False
    
    def validate_market_intelligence(self) -> bool:
        """Validate market intelligence module integration"""
        try:
            init_path = os.path.join(
                self.project_root, 'src', 'market_intelligence', '__init__.py'
            )
            
            if not os.path.exists(init_path):
                logger.error("Market intelligence __init__.py not found")
                return False
            
            with open(init_path, 'r') as f:
                content = f.read()
            
            # Check for regime detector exports
            required_exports = [
                'RegimeDetector',
                'regime_detector',
                'MarketRegime',
                'RegimeAnalysis'
            ]
            
            for export in required_exports:
                if export not in content:
                    logger.error(f"Missing export: {export}")
                    return False
            
            logger.info("Market intelligence integration validated")
            return True
            
        except Exception as e:
            logger.error(f"Market intelligence validation failed: {e}")
            return False
    
    def validate_code_structure(self) -> bool:
        """Validate overall code structure"""
        try:
            # Check that all required files exist
            required_files = [
                'src/market_intelligence/regime_detector.py',
                'src/signal_engine/signal_generator.py',
                'src/market_intelligence/__init__.py',
                'scripts/test_phase4_signal_enhancement.py'
            ]
            
            for file_path in required_files:
                full_path = os.path.join(self.project_root, file_path)
                if not os.path.exists(full_path):
                    logger.error(f"Required file missing: {file_path}")
                    return False
            
            # Check file sizes (should not be empty)
            for file_path in required_files:
                full_path = os.path.join(self.project_root, file_path)
                if os.path.getsize(full_path) < 100:  # Minimum reasonable size
                    logger.error(f"File too small (possibly empty): {file_path}")
                    return False
            
            logger.info("Code structure validated")
            return True
            
        except Exception as e:
            logger.error(f"Code structure validation failed: {e}")
            return False
    
    def generate_implementation_summary(self) -> Dict[str, Any]:
        """Generate implementation summary"""
        return {
            'phase': 'Phase 4: Signal Quality Enhancement',
            'status': 'COMPLETED' if all(self.validation_results.values()) else 'NEEDS_ATTENTION',
            'validation_results': self.validation_results,
            'new_modules': [
                'src/market_intelligence/regime_detector.py'
            ],
            'enhanced_modules': [
                'src/signal_engine/signal_generator.py',
                'src/market_intelligence/__init__.py'
            ],
            'test_scripts': [
                'scripts/test_phase4_signal_enhancement.py',
                'scripts/validate_phase4_implementation.py'
            ],
            'key_features': [
                'Market regime detection (bull/bear/sideways)',
                'Volatility-based asset filtering',
                'Regime-aligned signal filtering',
                'Enhanced signal quality scoring',
                'Market microstructure validation',
                'Multi-timeframe volatility analysis integration'
            ]
        }


def main():
    """Main validation execution"""
    validator = Phase4Validator()
    success = validator.validate_all()
    
    # Generate summary
    summary = validator.generate_implementation_summary()
    
    print("\n" + "="*60)
    print("PHASE 4 IMPLEMENTATION SUMMARY")
    print("="*60)
    print(f"Status: {summary['status']}")
    print(f"Phase: {summary['phase']}")
    
    print("\n📁 New Modules:")
    for module in summary['new_modules']:
        print(f"  ✨ {module}")
    
    print("\n🔧 Enhanced Modules:")
    for module in summary['enhanced_modules']:
        print(f"  ⚡ {module}")
    
    print("\n🧪 Test Scripts:")
    for script in summary['test_scripts']:
        print(f"  🔬 {script}")
    
    print("\n🎯 Key Features:")
    for feature in summary['key_features']:
        print(f"  🚀 {feature}")
    
    print("\n" + "="*60)
    
    if success:
        print("🎉 Phase 4 Signal Quality Enhancement: IMPLEMENTATION VALIDATED")
        return 0
    else:
        print("❌ Phase 4 Signal Quality Enhancement: VALIDATION FAILED")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
