#!/usr/bin/env python3
"""
Phase 4 Signal Quality Enhancement Testing Script
Tests the enhanced signal generation with volatility-based filtering and market regime detection
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import config
from src.signal_engine.signal_generator import SignalGenerator
from src.market_intelligence import (
    regime_detector, volatility_analyzer, MarketRegime, RegimeAnalysis
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Phase4TestSuite:
    """Comprehensive test suite for Phase 4 signal quality enhancements"""
    
    def __init__(self):
        self.signal_generator = SignalGenerator(config)
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run all Phase 4 tests"""
        logger.info("🚀 Starting Phase 4 Signal Quality Enhancement Tests")
        
        tests = [
            ("Market Regime Detection", self.test_market_regime_detection),
            ("Volatility-Based Asset Filtering", self.test_volatility_filtering),
            ("Enhanced Signal Generation", self.test_enhanced_signal_generation),
            ("Regime-Based Signal Filtering", self.test_regime_signal_filtering),
            ("Market Intelligence Integration", self.test_market_intelligence_integration),
            ("Signal Quality Scoring", self.test_signal_quality_scoring)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n📋 Running: {test_name}")
                result = await test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                
                self.test_results[test_name] = result
                
            except Exception as e:
                logger.error(f"💥 {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
        
        # Print summary
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n🎯 Phase 4 Test Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 Phase 4 Signal Quality Enhancement: OPERATIONAL")
        else:
            logger.warning("⚠️  Phase 4 Signal Quality Enhancement: NEEDS ATTENTION")
        
        return success_rate >= 80
    
    async def test_market_regime_detection(self) -> bool:
        """Test market regime detection functionality"""
        try:
            # Test regime detection
            regime_analysis = await regime_detector.detect_market_regime()
            
            # Validate regime analysis structure
            required_fields = [
                'current_regime', 'regime_strength', 'trend_direction',
                'momentum_score', 'correlation_strength', 'regime_change_probability'
            ]
            
            for field in required_fields:
                if not hasattr(regime_analysis, field):
                    logger.error(f"Missing field in regime analysis: {field}")
                    return False
            
            # Validate regime is a valid MarketRegime
            if not isinstance(regime_analysis.current_regime, MarketRegime):
                logger.error("Invalid regime type")
                return False
            
            # Validate numeric ranges
            if not (0 <= regime_analysis.regime_strength <= 1):
                logger.error("Invalid regime strength range")
                return False
            
            if not (-1 <= regime_analysis.trend_direction <= 1):
                logger.error("Invalid trend direction range")
                return False
            
            logger.info(f"Detected regime: {regime_analysis.current_regime.value}")
            logger.info(f"Regime strength: {regime_analysis.regime_strength:.3f}")
            logger.info(f"Trend direction: {regime_analysis.trend_direction:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Market regime detection test failed: {e}")
            return False
    
    async def test_volatility_filtering(self) -> bool:
        """Test volatility-based asset filtering"""
        try:
            # Create mock assets for testing
            mock_assets = [
                {
                    'symbol': 'TEST1',
                    'address': '0x1234567890123456789012345678901234567890',
                    'liquidity_usd': 100000,
                    'volume_24h': 50000
                },
                {
                    'symbol': 'TEST2', 
                    'address': '0x2345678901234567890123456789012345678901',
                    'liquidity_usd': 200000,
                    'volume_24h': 75000
                }
            ]
            
            # Test volatility filtering
            filtered_assets = await self.signal_generator._filter_assets_by_volatility(mock_assets)
            
            # Validate filtering results
            if not isinstance(filtered_assets, list):
                logger.error("Volatility filtering did not return a list")
                return False
            
            # Check that assets have volatility metadata
            for asset in filtered_assets:
                if 'volatility_priority' not in asset:
                    logger.error("Asset missing volatility_priority field")
                    return False
            
            logger.info(f"Volatility filtering: {len(filtered_assets)}/{len(mock_assets)} assets passed")
            
            return True
            
        except Exception as e:
            logger.error(f"Volatility filtering test failed: {e}")
            return False
    
    async def test_enhanced_signal_generation(self) -> bool:
        """Test enhanced signal generation with Phase 4 features"""
        try:
            # Create mock scan result
            mock_scan_result = {
                'assets': [
                    {
                        'symbol': 'TESTASSET',
                        'address': '0x1234567890123456789012345678901234567890',
                        'liquidity_usd': 500000,
                        'volume_24h': 250000,
                        'volume_ratio': 0.5,
                        'sentiment_score': 0.7
                    }
                ],
                'total_scanned': 1,
                'total_filtered': 1
            }
            
            # Test enhanced signal generation
            signals = await self.signal_generator.generate_signals(mock_scan_result)
            
            # Validate signals structure
            if not isinstance(signals, list):
                logger.error("Signal generation did not return a list")
                return False
            
            # Check for Phase 4 enhancements in signal metadata
            for signal in signals:
                if 'market_intelligence_metadata' not in signal:
                    logger.warning("Signal missing market intelligence metadata")
                
                # Validate required signal fields
                required_fields = [
                    'asset', 'signal_direction', 'signal_strength', 
                    'confidence', 'timestamp'
                ]
                
                for field in required_fields:
                    if field not in signal:
                        logger.error(f"Signal missing required field: {field}")
                        return False
            
            logger.info(f"Enhanced signal generation: {len(signals)} signals generated")
            
            return True
            
        except Exception as e:
            logger.error(f"Enhanced signal generation test failed: {e}")
            return False
    
    async def test_regime_signal_filtering(self) -> bool:
        """Test regime-based signal filtering"""
        try:
            # Test signal filtering for different regimes
            test_regimes = [
                MarketRegime.BULL_STRONG,
                MarketRegime.BEAR_STRONG,
                MarketRegime.SIDEWAYS_TIGHT
            ]
            
            test_signals = ["BUY", "SELL"]
            
            for regime in test_regimes:
                for signal_direction in test_signals:
                    should_filter = await regime_detector.should_filter_signal_by_regime(
                        signal_direction, regime
                    )
                    
                    # Validate filtering logic
                    if regime == MarketRegime.BULL_STRONG and signal_direction == "SELL":
                        if not should_filter:
                            logger.error("Should filter SELL signals in BULL_STRONG regime")
                            return False
                    
                    elif regime == MarketRegime.BEAR_STRONG and signal_direction == "BUY":
                        if not should_filter:
                            logger.error("Should filter BUY signals in BEAR_STRONG regime")
                            return False
                    
                    elif regime == MarketRegime.SIDEWAYS_TIGHT:
                        if not should_filter:
                            logger.error("Should filter all signals in SIDEWAYS_TIGHT regime")
                            return False
            
            logger.info("Regime-based signal filtering logic validated")
            
            return True
            
        except Exception as e:
            logger.error(f"Regime signal filtering test failed: {e}")
            return False
    
    async def test_market_intelligence_integration(self) -> bool:
        """Test integration with market intelligence modules"""
        try:
            # Test volatility analyzer integration
            vol_analysis = await volatility_analyzer.analyze_asset_volatility(
                "0x1234567890123456789012345678901234567890"
            )
            
            if not isinstance(vol_analysis, dict):
                logger.error("Volatility analyzer integration failed")
                return False
            
            # Test regime detector integration
            regime_analysis = await regime_detector.detect_market_regime()
            
            if not isinstance(regime_analysis, RegimeAnalysis):
                logger.error("Regime detector integration failed")
                return False
            
            # Test regime multiplier calculation
            multiplier = await regime_detector.get_regime_adjusted_signal_multiplier(
                regime_analysis.current_regime
            )
            
            if not isinstance(multiplier, (int, float)) or multiplier <= 0:
                logger.error("Invalid regime multiplier")
                return False
            
            logger.info("Market intelligence integration validated")
            
            return True
            
        except Exception as e:
            logger.error(f"Market intelligence integration test failed: {e}")
            return False
    
    async def test_signal_quality_scoring(self) -> bool:
        """Test enhanced signal quality scoring"""
        try:
            # Create mock signal for scoring
            mock_signal = {
                'signal_strength': 0.8,
                'confidence': 0.7,
                'market_intelligence_metadata': {
                    'regime_aligned': True,
                    'volatility_enhanced': True,
                    'regime_analysis': {
                        'regime_strength': 0.6
                    }
                }
            }
            
            # Test enhanced scoring
            score = self.signal_generator._calculate_enhanced_signal_score(mock_signal)
            
            # Validate score
            if not isinstance(score, (int, float)):
                logger.error("Invalid signal score type")
                return False
            
            if score <= 0:
                logger.error("Signal score should be positive")
                return False
            
            # Test that enhanced signals get higher scores
            base_score = mock_signal['signal_strength'] * mock_signal['confidence']
            if score <= base_score:
                logger.error("Enhanced signal should have higher score than base")
                return False
            
            logger.info(f"Signal quality scoring: base={base_score:.3f}, enhanced={score:.3f}")
            
            return True
            
        except Exception as e:
            logger.error(f"Signal quality scoring test failed: {e}")
            return False


async def main():
    """Main test execution"""
    test_suite = Phase4TestSuite()
    success = await test_suite.run_all_tests()
    
    if success:
        logger.info("\n🎉 Phase 4 Signal Quality Enhancement is ready for production!")
        return 0
    else:
        logger.error("\n❌ Phase 4 Signal Quality Enhancement needs fixes before production")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
