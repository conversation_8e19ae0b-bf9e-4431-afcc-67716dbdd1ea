"""
Market Regime Detection Engine
Identifies bull/bear/sideways market regimes using real data and correlation patterns
"""

import asyncio
import statistics
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
from enum import Enum

from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.simple_asset_provider import simple_asset_provider
from src.data_collectors.cache_manager import cache_manager, CacheKeys
from .volatility_analyzer import volatility_analyzer

logger = structlog.get_logger(__name__)


class MarketRegime(Enum):
    """Market regime classifications"""
    BULL_STRONG = "bull_strong"          # Strong uptrend with high momentum
    BULL_WEAK = "bull_weak"              # Weak uptrend with declining momentum
    BEAR_STRONG = "bear_strong"          # Strong downtrend with high momentum
    BEAR_WEAK = "bear_weak"              # Weak downtrend with declining momentum
    SIDEWAYS_TIGHT = "sideways_tight"    # Low volatility consolidation
    SIDEWAYS_VOLATILE = "sideways_volatile"  # High volatility but no clear direction
    TRANSITION_BULL = "transition_bull"   # Transitioning from bear to bull
    TRANSITION_BEAR = "transition_bear"   # Transitioning from bull to bear
    UNKNOWN = "unknown"                   # Insufficient data or unclear regime


@dataclass
class RegimeAnalysis:
    """Market regime analysis result"""
    current_regime: MarketRegime
    regime_strength: float  # 0-1, confidence in regime classification
    regime_duration_hours: int  # How long current regime has been active
    trend_direction: float  # -1 to 1, overall trend direction
    momentum_score: float  # 0-1, current momentum strength
    volatility_regime: str  # From volatility analyzer
    correlation_strength: float  # 0-1, how correlated assets are moving
    regime_change_probability: float  # 0-1, likelihood of regime change soon
    supporting_evidence: Dict[str, Any]
    analysis_timestamp: str


class RegimeDetector:
    """
    Detects market regimes using:
    - Multi-timeframe trend analysis
    - Volatility clustering patterns
    - Cross-asset correlation analysis
    - Momentum indicators
    - Real market microstructure data
    """
    
    def __init__(self):
        self.cache_ttl = 300  # 5 minutes cache
        self.logger = structlog.get_logger(__name__)
        
        # Regime detection parameters
        self.trend_lookback_hours = [4, 12, 24, 72]  # Multiple timeframes
        self.correlation_window_hours = 24
        self.momentum_window_hours = 6
        self.regime_change_threshold = 0.7
        
        # Major assets for market-wide analysis
        self.major_assets = [
            "******************************************",  # USDC
            "******************************************",  # WETH (example)
            "******************************************",  # WBTC
        ]
    
    async def detect_market_regime(self, reference_assets: Optional[List[str]] = None) -> RegimeAnalysis:
        """
        Detect current market regime using comprehensive analysis
        """
        cache_key = CacheKeys.market_intelligence("market_regime_analysis")
        
        # Check cache first
        cached_analysis = await cache_manager.get(cache_key)
        if cached_analysis:
            logger.debug("market_regime_cache_hit")
            return RegimeAnalysis(**cached_analysis)
        
        try:
            # Use provided assets or default major assets
            assets_to_analyze = reference_assets or self.major_assets
            
            # Multi-timeframe trend analysis
            trend_analysis = await self._analyze_multi_timeframe_trends(assets_to_analyze)
            
            # Volatility clustering analysis
            volatility_analysis = await self._analyze_volatility_clustering(assets_to_analyze)
            
            # Cross-asset correlation analysis
            correlation_analysis = await self._analyze_cross_asset_correlation(assets_to_analyze)
            
            # Momentum analysis
            momentum_analysis = await self._analyze_market_momentum(assets_to_analyze)
            
            # Combine analyses to determine regime
            regime_analysis = self._determine_market_regime(
                trend_analysis, volatility_analysis, correlation_analysis, momentum_analysis
            )
            
            # Cache the result
            await cache_manager.set(cache_key, regime_analysis.__dict__, self.cache_ttl)
            
            logger.info(
                "market_regime_detected",
                regime=regime_analysis.current_regime.value,
                strength=regime_analysis.regime_strength,
                trend_direction=regime_analysis.trend_direction,
                momentum=regime_analysis.momentum_score
            )
            
            return regime_analysis
            
        except Exception as e:
            logger.error("market_regime_detection_failed", error=str(e))
            return self._get_default_regime_analysis()
    
    async def _analyze_multi_timeframe_trends(self, assets: List[str]) -> Dict[str, Any]:
        """Analyze trends across multiple timeframes"""
        try:
            trend_scores = {}
            
            for timeframe_hours in self.trend_lookback_hours:
                timeframe_trends = []
                
                for asset_address in assets:
                    try:
                        # Get price data for timeframe
                        end_time = datetime.now()
                        start_time = end_time - timedelta(hours=timeframe_hours)
                        
                        price_data = await price_aggregator.get_historical_prices(
                            f"asset_{asset_address}", start_time, end_time, interval='1h'
                        )
                        
                        if price_data and len(price_data) > 2:
                            # Calculate trend strength
                            prices = [float(p.get('close', 0)) for p in price_data]
                            if prices and prices[0] > 0:
                                trend_change = (prices[-1] - prices[0]) / prices[0]
                                timeframe_trends.append(trend_change)
                    
                    except Exception as e:
                        logger.warning(f"trend_analysis_failed_for_asset", asset=asset_address, error=str(e))
                        continue
                
                if timeframe_trends:
                    avg_trend = statistics.mean(timeframe_trends)
                    trend_consistency = 1.0 - (statistics.stdev(timeframe_trends) / max(abs(avg_trend), 0.01))
                    trend_scores[f"{timeframe_hours}h"] = {
                        'average_trend': avg_trend,
                        'consistency': max(0, min(1, trend_consistency)),
                        'asset_count': len(timeframe_trends)
                    }
            
            return {
                'timeframe_trends': trend_scores,
                'overall_trend_direction': self._calculate_overall_trend(trend_scores),
                'trend_strength': self._calculate_trend_strength(trend_scores),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("multi_timeframe_trend_analysis_failed", error=str(e))
            return {'timeframe_trends': {}, 'overall_trend_direction': 0, 'trend_strength': 0}
    
    async def _analyze_volatility_clustering(self, assets: List[str]) -> Dict[str, Any]:
        """Analyze volatility clustering patterns"""
        try:
            volatility_regimes = []
            volatility_scores = []
            
            for asset_address in assets:
                try:
                    # Get volatility analysis from existing analyzer
                    vol_analysis = await volatility_analyzer.analyze_asset_volatility(asset_address)
                    
                    if vol_analysis and 'volatility_regime' in vol_analysis:
                        volatility_regimes.append(vol_analysis['volatility_regime'])
                        
                        # Extract volatility score
                        vol_windows = vol_analysis.get('volatility_windows', {})
                        if '24h' in vol_windows:
                            vol_score = vol_windows['24h'].get('realized_volatility', 0)
                            volatility_scores.append(vol_score)
                
                except Exception as e:
                    logger.warning(f"volatility_clustering_failed_for_asset", asset=asset_address, error=str(e))
                    continue
            
            # Analyze regime consistency
            regime_consistency = self._calculate_regime_consistency(volatility_regimes)
            avg_volatility = statistics.mean(volatility_scores) if volatility_scores else 0
            
            return {
                'volatility_regimes': volatility_regimes,
                'regime_consistency': regime_consistency,
                'average_volatility': avg_volatility,
                'volatility_clustering_score': self._calculate_clustering_score(volatility_scores),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("volatility_clustering_analysis_failed", error=str(e))
            return {'regime_consistency': 0, 'average_volatility': 0, 'volatility_clustering_score': 0}
    
    async def _analyze_cross_asset_correlation(self, assets: List[str]) -> Dict[str, Any]:
        """Analyze correlation patterns between assets"""
        try:
            # Get price data for all assets
            asset_returns = {}
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=self.correlation_window_hours)
            
            for asset_address in assets:
                try:
                    price_data = await price_aggregator.get_historical_prices(
                        f"asset_{asset_address}", start_time, end_time, interval='1h'
                    )
                    
                    if price_data and len(price_data) > 1:
                        prices = [float(p.get('close', 0)) for p in price_data]
                        returns = []
                        for i in range(1, len(prices)):
                            if prices[i-1] > 0:
                                ret = (prices[i] - prices[i-1]) / prices[i-1]
                                returns.append(ret)
                        
                        if returns:
                            asset_returns[asset_address] = returns
                
                except Exception as e:
                    logger.warning(f"correlation_data_failed_for_asset", asset=asset_address, error=str(e))
                    continue
            
            # Calculate correlation matrix
            correlations = []
            asset_list = list(asset_returns.keys())
            
            for i in range(len(asset_list)):
                for j in range(i+1, len(asset_list)):
                    asset1, asset2 = asset_list[i], asset_list[j]
                    returns1, returns2 = asset_returns[asset1], asset_returns[asset2]
                    
                    # Align returns to same length
                    min_len = min(len(returns1), len(returns2))
                    if min_len > 5:  # Need minimum data points
                        corr = np.corrcoef(returns1[:min_len], returns2[:min_len])[0, 1]
                        if not np.isnan(corr):
                            correlations.append(abs(corr))  # Use absolute correlation
            
            avg_correlation = statistics.mean(correlations) if correlations else 0
            correlation_consistency = 1.0 - (statistics.stdev(correlations) / max(avg_correlation, 0.01)) if len(correlations) > 1 else 0
            
            return {
                'average_correlation': avg_correlation,
                'correlation_consistency': max(0, min(1, correlation_consistency)),
                'correlation_count': len(correlations),
                'high_correlation_threshold': 0.7,
                'market_cohesion_score': min(1.0, avg_correlation * 2),  # Scale to 0-1
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("cross_asset_correlation_analysis_failed", error=str(e))
            return {'average_correlation': 0, 'correlation_consistency': 0, 'market_cohesion_score': 0}

    async def _analyze_market_momentum(self, assets: List[str]) -> Dict[str, Any]:
        """Analyze market momentum indicators"""
        try:
            momentum_scores = []
            volume_momentum = []

            end_time = datetime.now()
            start_time = end_time - timedelta(hours=self.momentum_window_hours)

            for asset_address in assets:
                try:
                    price_data = await price_aggregator.get_historical_prices(
                        f"asset_{asset_address}", start_time, end_time, interval='1h'
                    )

                    if price_data and len(price_data) > 3:
                        prices = [float(p.get('close', 0)) for p in price_data]
                        volumes = [float(p.get('volume', 0)) for p in price_data]

                        # Calculate price momentum (rate of change acceleration)
                        if len(prices) >= 4:
                            recent_change = (prices[-1] - prices[-2]) / max(prices[-2], 0.001)
                            earlier_change = (prices[-2] - prices[-3]) / max(prices[-3], 0.001)
                            momentum = recent_change - earlier_change
                            momentum_scores.append(momentum)

                        # Calculate volume momentum
                        if len(volumes) >= 3:
                            recent_vol = statistics.mean(volumes[-2:])
                            earlier_vol = statistics.mean(volumes[-4:-2]) if len(volumes) >= 4 else volumes[0]
                            if earlier_vol > 0:
                                vol_momentum = (recent_vol - earlier_vol) / earlier_vol
                                volume_momentum.append(vol_momentum)

                except Exception as e:
                    logger.warning(f"momentum_analysis_failed_for_asset", asset=asset_address, error=str(e))
                    continue

            avg_momentum = statistics.mean(momentum_scores) if momentum_scores else 0
            avg_volume_momentum = statistics.mean(volume_momentum) if volume_momentum else 0

            # Calculate momentum strength (0-1)
            momentum_strength = min(1.0, abs(avg_momentum) * 10)  # Scale momentum

            return {
                'price_momentum': avg_momentum,
                'volume_momentum': avg_volume_momentum,
                'momentum_strength': momentum_strength,
                'momentum_direction': 1 if avg_momentum > 0 else -1 if avg_momentum < 0 else 0,
                'momentum_consistency': self._calculate_momentum_consistency(momentum_scores),
                'analysis_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("market_momentum_analysis_failed", error=str(e))
            return {'price_momentum': 0, 'volume_momentum': 0, 'momentum_strength': 0, 'momentum_direction': 0}

    def _determine_market_regime(self, trend_analysis: Dict, volatility_analysis: Dict,
                                correlation_analysis: Dict, momentum_analysis: Dict) -> RegimeAnalysis:
        """Combine all analyses to determine market regime"""
        try:
            # Extract key metrics
            trend_direction = trend_analysis.get('overall_trend_direction', 0)
            trend_strength = trend_analysis.get('trend_strength', 0)
            avg_volatility = volatility_analysis.get('average_volatility', 0)
            regime_consistency = volatility_analysis.get('regime_consistency', 0)
            correlation_strength = correlation_analysis.get('market_cohesion_score', 0)
            momentum_strength = momentum_analysis.get('momentum_strength', 0)
            momentum_direction = momentum_analysis.get('momentum_direction', 0)

            # Determine regime based on combined factors
            regime = MarketRegime.UNKNOWN
            regime_strength = 0.0

            # Strong trend conditions
            if trend_strength > 0.6 and momentum_strength > 0.5:
                if trend_direction > 0.3:
                    regime = MarketRegime.BULL_STRONG if momentum_direction > 0 else MarketRegime.BULL_WEAK
                elif trend_direction < -0.3:
                    regime = MarketRegime.BEAR_STRONG if momentum_direction < 0 else MarketRegime.BEAR_WEAK
                regime_strength = min(trend_strength, momentum_strength)

            # Sideways conditions
            elif abs(trend_direction) < 0.2 and trend_strength < 0.4:
                if avg_volatility > 0.03:  # High volatility threshold
                    regime = MarketRegime.SIDEWAYS_VOLATILE
                else:
                    regime = MarketRegime.SIDEWAYS_TIGHT
                regime_strength = 1.0 - trend_strength  # Inverse of trend strength

            # Transition conditions
            elif trend_strength < 0.5 and momentum_strength > 0.3:
                if momentum_direction > 0 and trend_direction < 0:
                    regime = MarketRegime.TRANSITION_BULL
                elif momentum_direction < 0 and trend_direction > 0:
                    regime = MarketRegime.TRANSITION_BEAR
                regime_strength = momentum_strength

            # Calculate regime change probability
            regime_change_prob = self._calculate_regime_change_probability(
                trend_strength, momentum_strength, regime_consistency, correlation_strength
            )

            # Estimate regime duration (simplified)
            regime_duration = self._estimate_regime_duration(trend_analysis, momentum_analysis)

            supporting_evidence = {
                'trend_analysis': trend_analysis,
                'volatility_analysis': volatility_analysis,
                'correlation_analysis': correlation_analysis,
                'momentum_analysis': momentum_analysis,
                'decision_factors': {
                    'trend_direction': trend_direction,
                    'trend_strength': trend_strength,
                    'momentum_strength': momentum_strength,
                    'volatility_level': avg_volatility,
                    'correlation_strength': correlation_strength
                }
            }

            return RegimeAnalysis(
                current_regime=regime,
                regime_strength=regime_strength,
                regime_duration_hours=regime_duration,
                trend_direction=trend_direction,
                momentum_score=momentum_strength,
                volatility_regime=volatility_analysis.get('volatility_regimes', ['unknown'])[0] if volatility_analysis.get('volatility_regimes') else 'unknown',
                correlation_strength=correlation_strength,
                regime_change_probability=regime_change_prob,
                supporting_evidence=supporting_evidence,
                analysis_timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error("regime_determination_failed", error=str(e))
            return self._get_default_regime_analysis()

    def _calculate_overall_trend(self, trend_scores: Dict) -> float:
        """Calculate overall trend direction from multiple timeframes"""
        if not trend_scores:
            return 0.0

        weighted_trends = []
        weights = {'4h': 0.4, '12h': 0.3, '24h': 0.2, '72h': 0.1}  # Favor shorter timeframes

        for timeframe, data in trend_scores.items():
            weight = weights.get(timeframe, 0.1)
            trend = data.get('average_trend', 0)
            consistency = data.get('consistency', 0)
            weighted_trends.append(trend * weight * consistency)

        return sum(weighted_trends) / max(sum(weights.values()), 0.1)

    def _calculate_trend_strength(self, trend_scores: Dict) -> float:
        """Calculate overall trend strength"""
        if not trend_scores:
            return 0.0

        strengths = []
        for data in trend_scores.values():
            trend = abs(data.get('average_trend', 0))
            consistency = data.get('consistency', 0)
            strength = min(1.0, trend * 10) * consistency  # Scale and weight by consistency
            strengths.append(strength)

        return statistics.mean(strengths) if strengths else 0.0

    def _calculate_regime_consistency(self, volatility_regimes: List[str]) -> float:
        """Calculate consistency of volatility regimes across assets"""
        if not volatility_regimes:
            return 0.0

        # Count regime occurrences
        regime_counts = {}
        for regime in volatility_regimes:
            regime_counts[regime] = regime_counts.get(regime, 0) + 1

        # Calculate consistency as percentage of most common regime
        max_count = max(regime_counts.values()) if regime_counts else 0
        return max_count / len(volatility_regimes) if volatility_regimes else 0.0

    def _calculate_clustering_score(self, volatility_scores: List[float]) -> float:
        """Calculate volatility clustering score"""
        if len(volatility_scores) < 2:
            return 0.0

        # High clustering = low standard deviation relative to mean
        mean_vol = statistics.mean(volatility_scores)
        if mean_vol == 0:
            return 0.0

        std_vol = statistics.stdev(volatility_scores)
        clustering_score = 1.0 - (std_vol / mean_vol)
        return max(0.0, min(1.0, clustering_score))

    def _calculate_momentum_consistency(self, momentum_scores: List[float]) -> float:
        """Calculate consistency of momentum across assets"""
        if len(momentum_scores) < 2:
            return 0.0

        # Count positive vs negative momentum
        positive_count = sum(1 for m in momentum_scores if m > 0)
        negative_count = sum(1 for m in momentum_scores if m < 0)

        # Consistency is when most assets move in same direction
        total_count = len(momentum_scores)
        max_directional = max(positive_count, negative_count)
        return max_directional / total_count if total_count > 0 else 0.0

    def _calculate_regime_change_probability(self, trend_strength: float, momentum_strength: float,
                                           regime_consistency: float, correlation_strength: float) -> float:
        """Calculate probability of regime change"""
        # High regime change probability when:
        # - Trend strength is weakening
        # - Momentum is inconsistent with trend
        # - Low regime consistency
        # - Low correlation (market fragmentation)

        stability_factors = [
            trend_strength,
            momentum_strength,
            regime_consistency,
            correlation_strength
        ]

        avg_stability = statistics.mean(stability_factors)
        change_probability = 1.0 - avg_stability

        return max(0.0, min(1.0, change_probability))

    def _estimate_regime_duration(self, trend_analysis: Dict, momentum_analysis: Dict) -> int:
        """Estimate how long current regime has been active (simplified)"""
        # This is a simplified estimation - in practice would need historical regime tracking
        trend_strength = trend_analysis.get('trend_strength', 0)
        momentum_strength = momentum_analysis.get('momentum_strength', 0)

        # Strong regimes tend to last longer
        base_duration = 12  # 12 hours base
        strength_multiplier = (trend_strength + momentum_strength) / 2

        estimated_duration = int(base_duration * (1 + strength_multiplier))
        return min(72, max(4, estimated_duration))  # Cap between 4-72 hours

    def _get_default_regime_analysis(self) -> RegimeAnalysis:
        """Return default regime analysis when detection fails"""
        return RegimeAnalysis(
            current_regime=MarketRegime.UNKNOWN,
            regime_strength=0.0,
            regime_duration_hours=12,
            trend_direction=0.0,
            momentum_score=0.0,
            volatility_regime='unknown',
            correlation_strength=0.0,
            regime_change_probability=0.5,
            supporting_evidence={},
            analysis_timestamp=datetime.now().isoformat()
        )

    async def get_regime_adjusted_signal_multiplier(self, current_regime: MarketRegime) -> float:
        """Get signal strength multiplier based on current market regime"""
        regime_multipliers = {
            MarketRegime.BULL_STRONG: 1.2,      # Favor bullish signals
            MarketRegime.BULL_WEAK: 1.0,        # Neutral
            MarketRegime.BEAR_STRONG: 1.2,      # Favor bearish signals
            MarketRegime.BEAR_WEAK: 1.0,        # Neutral
            MarketRegime.SIDEWAYS_TIGHT: 0.7,   # Reduce signal strength in tight ranges
            MarketRegime.SIDEWAYS_VOLATILE: 0.9, # Slightly reduce in volatile sideways
            MarketRegime.TRANSITION_BULL: 1.1,   # Slightly favor bullish in transition
            MarketRegime.TRANSITION_BEAR: 1.1,   # Slightly favor bearish in transition
            MarketRegime.UNKNOWN: 0.8           # Conservative in unknown regimes
        }

        return regime_multipliers.get(current_regime, 0.8)

    async def should_filter_signal_by_regime(self, signal_direction: str, current_regime: MarketRegime) -> bool:
        """Determine if signal should be filtered based on market regime"""
        # Filter signals that go against strong regime trends
        if current_regime == MarketRegime.BULL_STRONG and signal_direction == "SELL":
            return True  # Filter sell signals in strong bull market
        elif current_regime == MarketRegime.BEAR_STRONG and signal_direction == "BUY":
            return True  # Filter buy signals in strong bear market
        elif current_regime == MarketRegime.SIDEWAYS_TIGHT:
            return True  # Filter all signals in tight sideways markets

        return False  # Don't filter signal


# Global instance
regime_detector = RegimeDetector()
