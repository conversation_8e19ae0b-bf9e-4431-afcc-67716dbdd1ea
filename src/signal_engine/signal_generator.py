"""
AI-Powered Signal Generation Engine - Phase 3 Implementation
Combines LSTM predictions with DeepSeek AI analysis for trading signals
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from config.settings import Config
from src.ml_models.inference_engine import inference_engine, TradingSignal
from src.data_collectors.asset_selector import asset_selector
from src.data_collectors.price_aggregator import price_aggregator
from src.market_intelligence import (
    volatility_analyzer, regime_detector, probability_engine, liquidity_analyzer,
    MarketRegime, RegimeAnalysis
)


class SignalGenerator:
    """AI-powered signal generation using LSTM and DeepSeek with Phase 4 enhancements"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Signal generation parameters
        self.min_confidence = config.CONFIDENCE_THRESHOLD
        self.max_signals_per_cycle = 10

        # Asset filtering parameters
        self.min_liquidity = config.MIN_LIQUIDITY_USD
        self.min_volume_multiplier = config.MIN_VOLUME_MULTIPLIER

        # Phase 4: Volatility-based filtering parameters
        self.min_volatility_expansion_threshold = 0.15  # 15% volatility expansion
        self.volatility_regime_filter = True
        self.market_regime_filter = True

        # Phase 4: Signal quality enhancement parameters
        self.volatility_signal_multiplier = 1.2  # Boost signals with volatility expansion
        self.regime_alignment_bonus = 1.1  # Bonus for regime-aligned signals
        self.microstructure_validation = True  # Use liquidity analysis for validation

    async def scan_assets(self) -> Dict[str, Any]:
        """Scan and filter assets using Phase 2 data infrastructure"""

        try:
            self.logger.info("Scanning assets for trading opportunities")

            # Get top assets from asset selector
            top_assets = await asset_selector.get_top_assets()

            if not top_assets:
                self.logger.warning("No assets returned from asset selector")
                return {'assets': [], 'total_scanned': 0}

            # Filter assets based on our criteria
            filtered_assets = []

            for asset in top_assets:
                try:
                    # Extract asset information
                    asset_symbol = asset.get('trading_token', {}).get('symbol', 'UNKNOWN')
                    liquidity_usd = asset.get('liquidity_usd', 0)
                    volume_24h = asset.get('volume_24h_usd', 0)
                    avg_volume = asset.get('avg_volume_7d', volume_24h)  # Use current volume as fallback

                    # Apply filters
                    if liquidity_usd < self.min_liquidity:
                        continue

                    # Use volume to liquidity ratio instead since we don't have historical data
                    volume_ratio = asset.get('volume_to_liquidity_ratio', 1.0)
                    if volume_ratio < 0.1:  # More lenient threshold
                        continue

                    # Add to filtered list
                    filtered_assets.append({
                        'symbol': asset_symbol,
                        'address': asset.get('trading_token', {}).get('address'),
                        'liquidity_usd': liquidity_usd,
                        'volume_24h': volume_24h,
                        'volume_ratio': volume_ratio,
                        'sentiment_score': asset.get('sentiment_score', 0.5),
                        'raw_data': asset
                    })

                except Exception as e:
                    self.logger.warning(f"Error processing asset {asset}: {e}")
                    continue

            # Sort by combined score (liquidity + volume + sentiment)
            filtered_assets.sort(
                key=lambda x: (x['liquidity_usd'] * x['volume_ratio'] * x['sentiment_score']),
                reverse=True
            )

            # Limit to top assets
            top_filtered = filtered_assets[:self.config.TOP_ASSETS_COUNT]

            self.logger.info(f"Asset scan completed: {len(top_filtered)}/{len(top_assets)} assets passed filters")

            return {
                'assets': top_filtered,
                'total_scanned': len(top_assets),
                'total_filtered': len(top_filtered),
                'scan_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Asset scanning failed: {e}")
            return {'assets': [], 'total_scanned': 0, 'error': str(e)}

    async def generate_signals(self, scan_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI-powered trading signals with Phase 4 enhancements"""

        assets = scan_result.get('assets', [])
        if not assets:
            self.logger.info("No assets to analyze for signals")
            return []

        self.logger.info(f"Generating signals for {len(assets)} assets with Phase 4 enhancements")

        # Phase 4: Get current market regime
        current_regime = await self._get_current_market_regime()
        self.logger.info(f"Current market regime: {current_regime.current_regime.value}")

        signals = []
        processed_count = 0

        try:
            # Phase 4: Pre-filter assets based on volatility expansion
            filtered_assets = await self._filter_assets_by_volatility(assets)
            self.logger.info(f"Volatility filtering: {len(filtered_assets)}/{len(assets)} assets passed")

            # Process assets in batches for efficiency
            batch_size = 5
            for i in range(0, len(filtered_assets), batch_size):
                batch = filtered_assets[i:i + batch_size]

                # Generate signals for batch
                batch_signals = await self._process_asset_batch(batch)

                # Phase 4: Enhanced signal filtering and validation
                for signal_data in batch_signals:
                    if signal_data and await self._is_valid_enhanced_signal(signal_data, current_regime):
                        # Phase 4: Apply market intelligence enhancements
                        enhanced_signal = await self._enhance_signal_with_market_intelligence(
                            signal_data, current_regime
                        )
                        signals.append(self._format_signal_output(enhanced_signal))

                processed_count += len(batch)

                # Respect rate limits
                if i + batch_size < len(filtered_assets):
                    await asyncio.sleep(1)

                # Stop if we have enough signals
                if len(signals) >= self.max_signals_per_cycle:
                    break

            # Phase 4: Enhanced signal sorting with market intelligence
            signals.sort(
                key=lambda x: self._calculate_enhanced_signal_score(x),
                reverse=True
            )

            self.logger.info(
                f"Enhanced signal generation completed: {len(signals)} signals from {processed_count} assets "
                f"(regime: {current_regime.current_regime.value})"
            )

            return signals[:self.max_signals_per_cycle]

        except Exception as e:
            self.logger.error(f"Enhanced signal generation failed: {e}")
            return []

    async def _process_asset_batch(self, assets: List[Dict[str, Any]]) -> List[Optional[TradingSignal]]:
        """Process a batch of assets for signal generation"""

        tasks = []

        for asset in assets:
            task = self._generate_asset_signal(asset)
            tasks.append(task)

        # Execute batch with error handling
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        signals = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.warning(f"Failed to generate signal for {assets[i]['symbol']}: {result}")
                signals.append(None)
            else:
                signals.append(result)

        return signals

    async def _generate_asset_signal(self, asset: Dict[str, Any]) -> Optional[TradingSignal]:
        """Generate signal for a single asset"""

        try:
            symbol = asset['symbol']

            # Get recent market data for the asset
            market_data = await self._get_asset_market_data(asset)

            if market_data is None:
                self.logger.warning(f"No market data available for {symbol}")
                return None

            # Generate signal using inference engine
            signal = await inference_engine.generate_signal(symbol, market_data)

            return signal

        except Exception as e:
            self.logger.error(f"Failed to generate signal for {asset['symbol']}: {e}")
            return None

    async def _get_asset_market_data(self, asset: Dict[str, Any]) -> Optional[Any]:
        """Get market data for asset analysis"""

        try:
            # Get token address
            token_address = asset.get('address')
            if not token_address:
                return None

            # Get recent price history (last 2 hours of 1-minute data)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=2)

            market_data = await price_aggregator.get_historical_prices(
                asset['symbol'],
                start_time,
                end_time,
                interval='1m'
            )

            return market_data

        except Exception as e:
            self.logger.error(f"Failed to get market data for {asset['symbol']}: {e}")
            return None

    def _is_valid_signal(self, signal: TradingSignal) -> bool:
        """Validate if signal meets our criteria"""

        if signal is None:
            return False

        # Check confidence threshold
        if signal.confidence < self.min_confidence:
            return False

        # Check signal direction (must be BUY or SELL, not HOLD)
        if signal.signal_direction == "HOLD":
            return False

        # Check signal strength
        if signal.signal_strength < 0.5:
            return False

        # Check if we have valid predictions
        if signal.ensemble_prediction is None:
            return False

        return True

    # Phase 4: Enhanced signal validation and filtering methods

    async def _get_current_market_regime(self) -> RegimeAnalysis:
        """Get current market regime analysis"""
        try:
            return await regime_detector.detect_market_regime()
        except Exception as e:
            self.logger.warning(f"Failed to get market regime: {e}")
            # Return default regime analysis
            from src.market_intelligence.regime_detector import RegimeAnalysis, MarketRegime
            from datetime import datetime
            return RegimeAnalysis(
                current_regime=MarketRegime.UNKNOWN,
                regime_strength=0.0,
                regime_duration_hours=12,
                trend_direction=0.0,
                momentum_score=0.0,
                volatility_regime='unknown',
                correlation_strength=0.0,
                regime_change_probability=0.5,
                supporting_evidence={},
                analysis_timestamp=datetime.now().isoformat()
            )

    async def _filter_assets_by_volatility(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter assets based on volatility expansion patterns"""
        if not self.volatility_regime_filter:
            return assets

        filtered_assets = []

        for asset in assets:
            try:
                asset_address = asset.get('address')
                if not asset_address:
                    continue

                # Get volatility analysis
                vol_analysis = await volatility_analyzer.analyze_asset_volatility(asset_address)

                if vol_analysis:
                    # Check for volatility expansion
                    expected_daily_move = vol_analysis.get('expected_daily_move_pct', 0)
                    volatility_regime = vol_analysis.get('volatility_regime', 'unknown')

                    # Prioritize assets with volatility expansion
                    if (expected_daily_move > self.min_volatility_expansion_threshold or
                        'expansion' in volatility_regime.lower() or
                        'elevated' in volatility_regime.lower()):

                        # Add volatility metadata to asset
                        asset['volatility_analysis'] = vol_analysis
                        asset['volatility_priority'] = True
                        filtered_assets.append(asset)
                    elif 'high' in volatility_regime.lower():
                        # Include high volatility assets with lower priority
                        asset['volatility_analysis'] = vol_analysis
                        asset['volatility_priority'] = False
                        filtered_assets.append(asset)

            except Exception as e:
                self.logger.warning(f"Volatility filtering failed for {asset.get('symbol', 'unknown')}: {e}")
                # Include asset without volatility data
                asset['volatility_priority'] = False
                filtered_assets.append(asset)

        return filtered_assets

    async def _is_valid_enhanced_signal(self, signal: TradingSignal, current_regime: RegimeAnalysis) -> bool:
        """Enhanced signal validation with market regime and volatility considerations"""
        # Basic validation first
        if not self._is_valid_signal(signal):
            return False

        try:
            # Phase 4: Market regime filtering
            if self.market_regime_filter:
                should_filter = await regime_detector.should_filter_signal_by_regime(
                    signal.signal_direction, current_regime.current_regime
                )
                if should_filter:
                    self.logger.debug(f"Signal filtered by regime: {signal.asset} {signal.signal_direction}")
                    return False

            # Phase 4: Microstructure validation
            if self.microstructure_validation:
                is_valid_microstructure = await self._validate_signal_microstructure(signal)
                if not is_valid_microstructure:
                    self.logger.debug(f"Signal failed microstructure validation: {signal.asset}")
                    return False

            return True

        except Exception as e:
            self.logger.warning(f"Enhanced signal validation failed for {signal.asset}: {e}")
            return True  # Default to allowing signal if validation fails

    async def _validate_signal_microstructure(self, signal: TradingSignal) -> bool:
        """Validate signal using market microstructure data"""
        try:
            # Extract token address from signal (simplified - would need proper mapping)
            asset_symbol = signal.asset

            # Get liquidity analysis
            # Note: This is simplified - in practice would need proper asset address mapping
            liquidity_analysis = await liquidity_analyzer.analyze_liquidity_flow(
                "0x0000000000000000000000000000000000000000"  # Placeholder
            )

            if liquidity_analysis:
                # Check for adequate liquidity
                total_liquidity = liquidity_analysis.get('current_snapshot', {}).get('total_liquidity_usd', 0)
                if total_liquidity < self.min_liquidity:
                    return False

                # Check for liquidity gaps that might affect execution
                liquidity_gaps = liquidity_analysis.get('liquidity_gaps', [])
                if liquidity_gaps:
                    # Check if gaps are significant
                    for gap in liquidity_gaps:
                        if gap.get('confidence_score', 0) > 0.7:
                            self.logger.debug(f"High-confidence liquidity gap detected for {asset_symbol}")
                            return False

            return True

        except Exception as e:
            self.logger.warning(f"Microstructure validation failed for {signal.asset}: {e}")
            return True  # Default to allowing signal

    async def _enhance_signal_with_market_intelligence(self, signal: TradingSignal,
                                                     current_regime: RegimeAnalysis) -> TradingSignal:
        """Enhance signal with market intelligence data"""
        try:
            # Phase 4: Apply regime-based signal strength adjustment
            regime_multiplier = await regime_detector.get_regime_adjusted_signal_multiplier(
                current_regime.current_regime
            )

            # Adjust signal strength based on regime
            original_strength = signal.signal_strength
            signal.signal_strength = min(1.0, original_strength * regime_multiplier)

            # Phase 4: Apply volatility expansion bonus
            if hasattr(signal, 'volatility_priority') and getattr(signal, 'volatility_priority', False):
                signal.signal_strength = min(1.0, signal.signal_strength * self.volatility_signal_multiplier)

            # Phase 4: Adjust confidence based on regime alignment
            if self._is_signal_regime_aligned(signal, current_regime):
                signal.confidence = min(1.0, signal.confidence * self.regime_alignment_bonus)

            # Add market intelligence metadata
            if not hasattr(signal, 'market_intelligence_metadata'):
                signal.market_intelligence_metadata = {}

            signal.market_intelligence_metadata.update({
                'regime_analysis': current_regime.__dict__,
                'regime_multiplier': regime_multiplier,
                'original_signal_strength': original_strength,
                'volatility_enhanced': getattr(signal, 'volatility_priority', False),
                'regime_aligned': self._is_signal_regime_aligned(signal, current_regime)
            })

            return signal

        except Exception as e:
            self.logger.warning(f"Signal enhancement failed for {signal.asset}: {e}")
            return signal

    def _is_signal_regime_aligned(self, signal: TradingSignal, current_regime: RegimeAnalysis) -> bool:
        """Check if signal direction aligns with current market regime"""
        regime = current_regime.current_regime
        direction = signal.signal_direction

        # Bull regimes favor buy signals
        if regime.value.startswith('bull') and direction == "BUY":
            return True
        # Bear regimes favor sell signals
        elif regime.value.startswith('bear') and direction == "SELL":
            return True
        # Transition regimes are neutral
        elif regime.value.startswith('transition'):
            return True

        return False

    def _calculate_enhanced_signal_score(self, signal_dict: Dict[str, Any]) -> float:
        """Calculate enhanced signal score for sorting"""
        base_score = signal_dict['signal_strength'] * signal_dict['confidence']

        # Phase 4: Add market intelligence bonuses
        metadata = signal_dict.get('market_intelligence_metadata', {})

        # Regime alignment bonus
        if metadata.get('regime_aligned', False):
            base_score *= 1.1

        # Volatility expansion bonus
        if metadata.get('volatility_enhanced', False):
            base_score *= 1.15

        # Regime strength bonus
        regime_analysis = metadata.get('regime_analysis', {})
        regime_strength = regime_analysis.get('regime_strength', 0)
        base_score *= (1 + regime_strength * 0.1)  # Up to 10% bonus

        return base_score

    def _format_signal_output(self, signal: TradingSignal) -> Dict[str, Any]:
        """Format signal for output to main bot"""

        return {
            'asset': signal.asset,
            'timestamp': signal.timestamp.isoformat(),
            'signal_direction': signal.signal_direction,
            'signal_strength': signal.signal_strength,
            'confidence': signal.confidence,
            'risk_level': signal.risk_level,
            'position_size': signal.position_size,
            'entry_price': signal.entry_price,
            'target_price': signal.target_price,
            'stop_loss': signal.stop_loss,
            'max_hold_time': signal.max_hold_time,

            # LSTM predictions
            'lstm_predictions': {
                timeframe: {
                    'predicted_change': pred.predicted_change,
                    'confidence': pred.confidence
                } if pred else None
                for timeframe, pred in signal.lstm_predictions.items()
            },

            # Ensemble prediction
            'ensemble_prediction': {
                'predicted_change': signal.ensemble_prediction.predicted_change,
                'confidence': signal.ensemble_prediction.confidence
            } if signal.ensemble_prediction else None,

            # AI analysis
            'sentiment_analysis': {
                'sentiment_score': signal.sentiment_analysis.sentiment_score,
                'confidence': signal.sentiment_analysis.confidence,
                'market_outlook': signal.sentiment_analysis.market_outlook,
                'reasoning': signal.sentiment_analysis.reasoning
            } if signal.sentiment_analysis else None,

            'market_intelligence': {
                'prediction_1m': signal.market_intelligence.prediction_1m,
                'prediction_5m': signal.market_intelligence.prediction_5m,
                'prediction_15m': signal.market_intelligence.prediction_15m,
                'confidence': signal.market_intelligence.confidence,
                'entry_recommendation': signal.market_intelligence.entry_recommendation
            } if signal.market_intelligence else None,

            # Metadata
            'processing_time': signal.processing_time,
            'model_versions': signal.model_versions,

            # Phase 4: Market Intelligence Metadata
            'market_intelligence_metadata': getattr(signal, 'market_intelligence_metadata', {}),
            'volatility_priority': getattr(signal, 'volatility_priority', False)
        }

    def get_signal_stats(self) -> Dict[str, Any]:
        """Get signal generation statistics"""

        return {
            'inference_engine_metrics': inference_engine.get_performance_metrics(),
            'configuration': {
                'min_confidence': self.min_confidence,
                'max_signals_per_cycle': self.max_signals_per_cycle,
                'min_liquidity': self.min_liquidity,
                'min_volume_multiplier': self.min_volume_multiplier,
                # Phase 4 configuration
                'min_volatility_expansion_threshold': self.min_volatility_expansion_threshold,
                'volatility_regime_filter': self.volatility_regime_filter,
                'market_regime_filter': self.market_regime_filter,
                'volatility_signal_multiplier': self.volatility_signal_multiplier,
                'regime_alignment_bonus': self.regime_alignment_bonus,
                'microstructure_validation': self.microstructure_validation
            }
        }
